---
type: "manual"
---

# COMPREHENSIVE REFLECTION FOR LEVEL 4 TASKS

> **TL;DR:** This document outlines a structured, comprehensive approach to reflection for Level 4 (Complex System) tasks, including system review, success and challenge analysis, strategic insights, and action planning.

## 🔍 COMPREHENSIVE REFLECTION OVERVIEW

Level 4 Complex System tasks require in-depth reflection to capture key insights, document successes and challenges, extract strategic lessons, and guide future improvements. This systematic reflection process ensures organizational learning and continuous improvement.

```mermaid
flowchart TD
    classDef phase fill:#f9d77e,stroke:#d9b95c,color:#000
    classDef artifact fill:#f4b8c4,stroke:#d498a4,color:#000
    classDef verification fill:#c5e8b7,stroke:#a5c897,color:#000
    
    Start([Begin Reflection<br>Process]) --> Template[Load Comprehensive<br>Reflection Template]
    Template --> SysReview[Conduct System<br>Review]
    SysReview --> ArchReview[Review Architecture<br>Decisions]
    ArchReview --> ImplementReview[Review Implementation<br>Approach]
    ImplementReview --> SuccessAnalysis[Document Successes<br>and Achievements]
    SuccessAnalysis --> ChallengeAnalysis[Document Challenges<br>and Solutions]
    ChallengeAnalysis --> Technical[Extract Technical<br>Insights]
    Technical --> Process[Extract Process<br>Insights]
    Process --> Business[Extract Business<br>Insights]
    Business --> Strategic[Define Strategic<br>Actions]
    Strategic --> Timeline[Analyze Timeline<br>Performance]
    Timeline --> Documentation[Complete Reflection<br>Documentation]
    Documentation --> Integration[Integrate with<br>Memory Bank]
    Integration --> Verification{Reflection<br>Verification}
    Verification -->|Pass| Complete([Reflection<br>Complete])
    Verification -->|Fail| Revise[Revise<br>Reflection]
    Revise --> Verification
    
    Template -.-> RT((Reflection<br>Template))
    SysReview -.-> SR((System<br>Review))
    SuccessAnalysis & ChallengeAnalysis -.-> SCD((Success/Challenge<br>Document))
    Technical & Process & Business -.-> Insights((Insight<br>Document))
    Strategic -.-> Actions((Strategic<br>Actions))
    
    class Start,Complete milestone
    class Template,SysReview,ArchReview,ImplementReview,SuccessAnalysis,ChallengeAnalysis,Technical,Process,Business,Strategic,Timeline,Documentation,Integration step
    class Verification verification
    class RT,SR,SCD,Insights,Actions artifact
```

## 📋 REFLECTION TEMPLATE STRUCTURE

### 1. System Overview

```markdown
## System Overview

### System Description
[Comprehensive description of the implemented system, including purpose, scope, and key features]

### System Context
[Description of how the system fits into the broader technical and business ecosystem]

### Key Components
- Component 1: [Description and purpose]
- Component 2: [Description and purpose]
- Component 3: [Description and purpose]

### System Architecture
[Summary of the architectural approach, key patterns, and design decisions]

### System Boundaries
[Description of system boundaries, interfaces, and integration points]

### Implementation Summary
[Overview of the implementation approach, technologies, and methods used]
```

### 2. Project Performance Analysis

```markdown
## Project Performance Analysis

### Timeline Performance
- **Planned Duration**: [X] weeks/months
- **Actual Duration**: [Y] weeks/months
- **Variance**: [+/-Z] weeks/months ([P]%)
- **Explanation**: [Analysis of timeline variances]

### Resource Utilization
- **Planned Resources**: [X] person-months
- **Actual Resources**: [Y] person-months
- **Variance**: [+/-Z] person-months ([P]%)
- **Explanation**: [Analysis of resource variances]

### Quality Metrics
- **Planned Quality Targets**: [List of quality targets]
- **Achieved Quality Results**: [List of achieved quality results]
- **Variance Analysis**: [Analysis of quality variances]

### Risk Management Effectiveness
- **Identified Risks**: [Number of risks identified]
- **Risks Materialized**: [Number and percentage of risks that occurred]
- **Mitigation Effectiveness**: [Effectiveness of risk mitigation strategies]
- **Unforeseen Risks**: [Description of unforeseen risks that emerged]
```

### 3. Achievements and Successes

```markdown
## Achievements and Successes

### Key Achievements
1. **Achievement 1**: [Description]
   - **Evidence**: [Concrete evidence of success]
   - **Impact**: [Business/technical impact]
   - **Contributing Factors**: [What enabled this success]

2. **Achievement 2**: [Description]
   - **Evidence**: [Concrete evidence of success]
   - **Impact**: [Business/technical impact]
   - **Contributing Factors**: [What enabled this success]

### Technical Successes
- **Success 1**: [Description of technical success]
  - **Approach Used**: [Description of approach]
  - **Outcome**: [Results achieved]
  - **Reusability**: [How this can be reused]

- **Success 2**: [Description of technical success]
  - **Approach Used**: [Description of approach]
  - **Outcome**: [Results achieved]
  - **Reusability**: [How this can be reused]

### Process Successes
- **Success 1**: [Description of process success]
  - **Approach Used**: [Description of approach]
  - **Outcome**: [Results achieved]
  - **Reusability**: [How this can be reused]

### Team Successes
- **Success 1**: [Description of team success]
  - **Approach Used**: [Description of approach]
  - **Outcome**: [Results achieved]
  - **Reusability**: [How this can be reused]
```

### 4. Challenges and Solutions

```markdown
## Challenges and Solutions

### Key Challenges
1. **Challenge 1**: [Description]
   - **Impact**: [Business/technical impact]
   - **Resolution Approach**: [How it was addressed]
   - **Outcome**: [Final result]
   - **Preventative Measures**: [How to prevent in future]

2. **Challenge 2**: [Description]
   - **Impact**: [Business/technical impact]
   - **Resolution Approach**: [How it was addressed]
   - **Outcome**: [Final result]
   - **Preventative Measures**: [How to prevent in future]

### Technical Challenges
- **Challenge 1**: [Description of technical challenge]
  - **Root Cause**: [Analysis of root cause]
  - **Solution**: [How it was solved]
  - **Alternative Approaches**: [Other approaches considered]
  - **Lessons Learned**: [Key takeaways]

- **Challenge 2**: [Description of technical challenge]
  - **Root Cause**: [Analysis of root cause]
  - **Solution**: [How it was solved]
  - **Alternative Approaches**: [Other approaches considered]
  - **Lessons Learned**: [Key takeaways]

### Process Challenges
- **Challenge 1**: [Description of process challenge]
  - **Root Cause**: [Analysis of root cause]
  - **Solution**: [How it was solved]
  - **Process Improvements**: [Improvements made or suggested]

### Unresolved Issues
- **Issue 1**: [Description of unresolved issue]
  - **Current Status**: [Status]
  - **Proposed Path Forward**: [Suggested next steps]
  - **Required Resources**: [What's needed to resolve]
```

### 5. Technical Insights

```markdown
## Technical Insights

### Architecture Insights
- **Insight 1**: [Description of architectural insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for future work]
  - **Recommendations**: [Suggested changes or actions]

- **Insight 2**: [Description of architectural insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for future work]
  - **Recommendations**: [Suggested changes or actions]

### Implementation Insights
- **Insight 1**: [Description of implementation insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for future work]
  - **Recommendations**: [Suggested changes or actions]

### Technology Stack Insights
- **Insight 1**: [Description of technology stack insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for future work]
  - **Recommendations**: [Suggested changes or actions]

### Performance Insights
- **Insight 1**: [Description of performance insight]
  - **Context**: [When/where this was observed]
  - **Metrics**: [Relevant performance metrics]
  - **Implications**: [What this means for future work]
  - **Recommendations**: [Suggested optimizations]

### Security Insights
- **Insight 1**: [Description of security insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for future work]
  - **Recommendations**: [Suggested security improvements]
```

### 6. Process Insights

```markdown
## Process Insights

### Planning Insights
- **Insight 1**: [Description of planning process insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for future work]
  - **Recommendations**: [Suggested process improvements]

### Development Process Insights
- **Insight 1**: [Description of development process insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for future work]
  - **Recommendations**: [Suggested process improvements]

### Testing Insights
- **Insight 1**: [Description of testing process insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for future work]
  - **Recommendations**: [Suggested process improvements]

### Collaboration Insights
- **Insight 1**: [Description of collaboration insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for future work]
  - **Recommendations**: [Suggested collaboration improvements]

### Documentation Insights
- **Insight 1**: [Description of documentation insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for future work]
  - **Recommendations**: [Suggested documentation improvements]
```

### 7. Business Insights

```markdown
## Business Insights

### Value Delivery Insights
- **Insight 1**: [Description of value delivery insight]
  - **Context**: [When/where this was observed]
  - **Business Impact**: [Impact on business outcomes]
  - **Recommendations**: [Suggested improvements]

### Stakeholder Insights
- **Insight 1**: [Description of stakeholder insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for stakeholder management]
  - **Recommendations**: [Suggested improvements]

### Market/User Insights
- **Insight 1**: [Description of market/user insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for product direction]
  - **Recommendations**: [Suggested improvements]

### Business Process Insights
- **Insight 1**: [Description of business process insight]
  - **Context**: [When/where this was observed]
  - **Implications**: [What this means for business processes]
  - **Recommendations**: [Suggested improvements]
```

### 8. Strategic Actions

```markdown
## Strategic Actions

### Immediate Actions
- **Action 1**: [Description of immediate action]
  - **Owner**: [Person responsible]
  - **Timeline**: [Expected completion date]
  - **Success Criteria**: [How to measure success]
  - **Resources Required**: [What's needed]
  - **Priority**: [High/Medium/Low]

- **Action 2**: [Description of immediate action]
  - **Owner**: [Person responsible]
  - **Timeline**: [Expected completion date]
  - **Success Criteria**: [How to measure success]
  - **Resources Required**: [What's needed]
  - **Priority**: [High/Medium/Low]

### Short-Term Improvements (1-3 months)
- **Improvement 1**: [Description of short-term improvement]
  - **Owner**: [Person responsible]
  - **Timeline**: [Expected completion date]
  - **Success Criteria**: [How to measure success]
  - **Resources Required**: [What's needed]
  - **Priority**: [High/Medium/Low]

### Medium-Term Initiatives (3-6 months)
- **Initiative 1**: [Description of medium-term initiative]
  - **Owner**: [Person responsible]
  - **Timeline**: [Expected completion date]
  - **Success Criteria**: [How to measure success]
  - **Resources Required**: [What's needed]
  - **Priority**: [High/Medium/Low]

### Long-Term Strategic Directions (6+ months)
- **Direction 1**: [Description of long-term strategic direction]
  - **Business Alignment**: [How this aligns with business strategy]
  - **Expected Impact**: [Anticipated outcomes]
  - **Key Milestones**: [Major checkpoints]
  - **Success Criteria**: [How to measure success]
```

### 9. Knowledge Transfer

```markdown
## Knowledge Transfer

### Key Learnings for Organization
- **Learning 1**: [Description of key organizational learning]
  - **Context**: [When/where this was learned]
  - **Applicability**: [Where this can be applied]
  - **Suggested Communication**: [How to share this]

### Technical Knowledge Transfer
- **Technical Knowledge 1**: [Description of technical knowledge]
  - **Audience**: [Who needs this knowledge]
  - **Transfer Method**: [How to transfer]
  - **Documentation**: [Where documented]

### Process Knowledge Transfer
- **Process Knowledge 1**: [Description of process knowledge]
  - **Audience**: [Who needs this knowledge]
  - **Transfer Method**: [How to transfer]
  - **Documentation**: [Where documented]

### Documentation Updates
- **Document 1**: [Name of document to update]
  - **Required Updates**: [What needs to be updated]
  - **Owner**: [Person responsible]
  - **Timeline**: [When it will be updated]
```

### 10. Reflection Summary

```markdown
## Reflection Summary

### Key Takeaways
- **Takeaway 1**: [Description of key takeaway]
- **Takeaway 2**: [Description of key takeaway]
- **Takeaway 3**: [Description of key takeaway]

### Success Patterns to Replicate
1. [Pattern 1 description]
2. [Pattern 2 description]
3. [Pattern 3 description]

### Issues to Avoid in Future
1. [Issue 1 description]
2. [Issue 2 description]
3. [Issue 3 description]

### Overall Assessment
[Comprehensive assessment of the project's success, challenges, and strategic value]

### Next Steps
[Clear description of immediate next steps following this reflection]
```

## 📋 REFLECTION PROCESS

### 1. Preparation

```mermaid
flowchart TD
    classDef step fill:#f9d77e,stroke:#d9b95c,color:#000
    classDef artifact fill:#f4b8c4,stroke:#d498a4,color:#000
    
    Start([Begin Reflection<br>Preparation]) --> Template[Load Reflection<br>Template]
    Template --> Data[Gather Project<br>Data]
    Data --> Metrics[Collect Performance<br>Metrics]
    Metrics --> Feedback[Gather Stakeholder<br>Feedback]
    Feedback --> Schedule[Schedule Reflection<br>Session]
    Schedule --> Participants[Identify<br>Participants]
    Participants --> Agenda[Create Session<br>Agenda]
    Agenda --> Complete([Preparation<br>Complete])
    
    Template -.-> TDoc((Reflection<br>Template))
    Data -.-> ProjData((Project<br>Data))
    Metrics -.-> MetricsDoc((Performance<br>Metrics))
    Feedback -.-> FeedbackDoc((Stakeholder<br>Feedback))
    
    class Start,Complete milestone
    class Template,Data,Metrics,Feedback,Schedule,Participants,Agenda step
    class TDoc,ProjData,MetricsDoc,FeedbackDoc artifact
```

**Key Preparation Steps:**
1. Load the comprehensive reflection template
2. Gather project data (tasks.md, documentation, artifacts)
3. Collect performance metrics (timeline, resource utilization, quality)
4. Gather stakeholder feedback (internal and external)
5. Schedule reflection session(s) with key participants
6. Prepare session agenda and pre-work materials

### 2. Conducting the Reflection Session

```mermaid
flowchart TD
    classDef step fill:#f9d77e,stroke:#d9b95c,color:#000
    classDef artifact fill:#f4b8c4,stroke:#d498a4,color:#000
    
    Start([Begin Reflection<br>Session]) --> Intro[Introduction and<br>Context Setting]
    Intro --> Project[Project Overview<br>Presentation]
    Project --> Success[Success<br>Identification]
    Success --> Challenge[Challenge<br>Identification]
    Challenge --> Root[Root Cause<br>Analysis]
    Root --> Insights[Insight<br>Generation]
    Insights --> Actions[Action<br>Planning]
    Actions --> Documentation[Document<br>Outcomes]
    Documentation --> Next[Define Next<br>Steps]
    Next --> Complete([Session<br>Complete])
    
    Success -.-> SuccessDoc((Success<br>Document))
    Challenge -.-> ChallengeDoc((Challenge<br>Document))
    Insights -.-> InsightDoc((Insight<br>Document))
    Actions -.-> ActionDoc((Action<br>Plan))
    
    class Start,Complete milestone
    class Intro,Project,Success,Challenge,Root,Insights,Actions,Documentation,Next step
    class SuccessDoc,ChallengeDoc,InsightDoc,ActionDoc artifact
```

**Session Format:**
- **Duration**: 2-4 hours (may be split across multiple sessions)
- **Participants**: Project team, key stakeholders, technical leads
- **Facilitation**: Neutral facilitator to guide the process
- **Documentation**: Dedicated scribe to capture insights and actions

### 3. Documentation and Integration

```mermaid
flowchart TD
    classDef step fill:#f9d77e,stroke:#d9b95c,color:#000
    classDef artifact fill:#f4b8c4,stroke:#d498a4,color:#000
    classDef verification fill:#c5e8b7,stroke:#a5c897,color:#000
    
    Start([Begin Documentation<br>and Integration]) --> Draft[Draft Reflection<br>Document]
    Draft --> Review[Review with<br>Key Stakeholders]
    Review --> Revise[Incorporate<br>Feedback]
    Revise --> Finalize[Finalize<br>Document]
    Finalize --> UpdateMB[Update Memory<br>Bank]
    UpdateMB --> ActionReg[Create Action<br>Register]
    ActionReg --> Archive[Archive Project<br>Documents]
    Archive --> Verification{Documentation<br>Verification}
    Verification -->|Pass| Complete([Documentation<br>Complete])
    Verification -->|Fail| MoreRevision[Address<br>Documentation Gaps]
    MoreRevision --> Verification
    
    Draft -.-> DraftDoc((Draft<br>Document))
    Finalize -.-> FinalDoc((Final<br>Reflection))
    ActionReg -.-> ActReg((Action<br>Register))
    
    class Start,Complete milestone
    class Draft,Review,Revise,Finalize,UpdateMB,ActionReg,Archive,MoreRevision step
    class Verification verification
    class DraftDoc,FinalDoc,ActReg artifact
```

**Key Documentation Steps:**
1. Draft comprehensive reflection document using the template
2. Review draft with key stakeholders and participants
3. Incorporate feedback and finalize document
4. Update Memory Bank with key insights and learnings
5. Create action register for tracking improvement actions
6. Archive project documents with reflection document
7. Verify documentation completeness and quality

## 📋 REFLECTION TECHNIQUES

### Root Cause Analysis

```mermaid
flowchart TD
    classDef step fill:#f9d77e,stroke:#d9b95c,color:#000
    
    Start([Identify<br>Challenge]) --> What[What<br>Happened?]
    What --> When[When Did<br>It Happen?]
    When --> Where[Where Did<br>It Happen?]
    Where --> Who[Who Was<br>Involved?]
    Who --> How[How Did<br>It Happen?]
    How --> Why1[Why Did<br>It Happen?]
    Why1 --> Why2[Why?<br>Deeper]
    Why2 --> Why3[Why?<br>Deeper]
    Why3 --> Why4[Why?<br>Deeper]
    Why4 --> Why5[Why?<br>Root Cause]
    Why5 --> Solution[Identify<br>Solution]
    Solution --> Prevent[Prevention<br>Strategy]
    
    class Start milestone
    class What,When,Where,Who,How,Why1,Why2,Why3,Why4,Why5,Solution,Prevent step
```

### Success Analysis

```mermaid
flowchart TD
    classDef step fill:#f9d77e,stroke:#d9b95c,color:#000
    
    Start([Identify<br>Success]) --> Define[Define the<br>Success]
    Define --> Impact[Measure the<br>Impact]
    Impact --> Factors[Identify Contributing<br>Factors]
    Factors --> Context[Consider<br>Context]
    Context --> Patterns[Identify<br>Patterns]
    Patterns --> Generalize[Generalize<br>Approach]
    Generalize --> Apply[Define Where<br>to Apply]
    
    class Start milestone
    class Define,Impact,Factors,Context,Patterns,Generalize,Apply step
```

### Insight Generation

```mermaid
flowchart TD
    classDef step fill:#f9d77e,stroke:#d9b95c,color:#000
    
    Start([Begin Insight<br>Generation]) --> Observe[Observe<br>Patterns]
    Observe --> Question[Question<br>Assumptions]
    Question --> Connect[Connect<br>Dots]
    Connect --> Contrast[Contrast with<br>Prior Knowledge]
    Contrast --> Hypothesize[Form<br>Hypothesis]
    Hypothesize --> Test[Test<br>Hypothesis]
    Test --> Refine[Refine<br>Insight]
    Refine --> Apply[Apply to<br>Future Work]
    
    class Start milestone
    class Observe,Question,Connect,Contrast,Hypothesize,Test,Refine,Apply step
```

## 📋 MEMORY BANK INTEGRATION

```mermaid
flowchart TD
    classDef memfile fill:#f4b8c4,stroke:#d498a4,color:#000
    classDef process fill:#f9d77e,stroke:#d9b95c,color:#000
    
    Reflection[Comprehensive<br>Reflection] --> PB[projectbrief.md]
    Reflection --> PC[productContext.md]
    Reflection --> AC[activeContext.md]
    Reflection --> SP[systemPatterns.md]
    Reflection --> TC[techContext.md]
    Reflection --> P[progress.md]
    
    PB & PC & AC & SP & TC & P --> MBI[Memory Bank<br>Integration]
    MBI --> Next[Enhanced Future<br>Projects]
    
    class PB,PC,AC,SP,TC,P memfile
    class Reflection,MBI,Next process
```

### Memory Bank Updates

Specific updates to make to Memory Bank files:

1. **projectbrief.md**
   - Update with strategic insights
   - Document key achievements
   - Incorporate lessons learned

2. **productContext.md**
   - Update with business insights
   - Document market/user insights
   - Include value delivery insights

3. **activeContext.md**
   - Update with current status
   - Document action items
   - Include next steps

4. **systemPatterns.md**
   - Update with architectural insights
   - Document successful patterns
   - Include technical knowledge

5. **techContext.md**
   - Update with implementation insights
   - Document technology stack insights
   - Include performance and security insights

6. **progress.md**
   - Update with final status
   - Document achievements
   - Include project metrics

## 📋 REFLECTION VERIFICATION CHECKLIST

```
✓ REFLECTION VERIFICATION CHECKLIST

System Review
- System overview complete and accurate? [YES/NO]
- Project performance metrics collected and analyzed? [YES/NO]
- System boundaries and interfaces described? [YES/NO]

Success and Challenge Analysis
- Key achievements documented with evidence? [YES/NO]
- Technical successes documented with approach? [YES/NO]
- Key challenges documented with resolutions? [YES/NO]
- Technical challenges documented with solutions? [YES/NO]
- Unresolved issues documented with path forward? [YES/NO]

Insight Generation
- Technical insights extracted and documented? [YES/NO]
- Process insights extracted and documented? [YES/NO]
- Business insights extracted and documented? [YES/NO]

Strategic Planning
- Immediate actions defined with owners? [YES/NO]
- Short-term improvements identified? [YES/NO]
- Medium-term initiatives planned? [YES/NO]
- Long-term strategic directions outlined? [YES/NO]

Knowledge Transfer
- Key learnings for organization documented? [YES/NO]
- Technical knowledge transfer planned? [YES/NO]
- Process knowledge transfer planned? [YES/NO]
- Documentation updates identified? [YES/NO]

Memory Bank Integration
- projectbrief.md updated with insights? [YES/NO]
- productContext.md updated with insights? [YES/NO]
- activeContext.md updated with insights? [YES/NO]
- systemPatterns.md updated with insights? [YES/NO]
- techContext.md updated with insights? [YES/NO]
- progress.md updated with final status? [YES/NO]
```

## 📋 MINIMAL MODE REFLECTION FORMAT

For situations requiring a more compact reflection:

```markdown
## Level 4 Task Reflection: [System Name]

### System Summary
- **Purpose**: [Brief description of system purpose]
- **Key Components**: [List of key components]
- **Architecture**: [Brief architecture description]

### Performance Summary
- **Timeline**: [Planned] vs [Actual] ([Variance])
- **Resources**: [Planned] vs [Actual] ([Variance])
- **Quality**: [Summary of quality achievements]

### Key Successes
1. [Success 1 with evidence and impact]
2. [Success 2 with evidence and impact]
3. [Success 3 with evidence and impact]

### Key Challenges
1. [Challenge 1 with resolution and lessons]
2. [Challenge 2 with resolution and lessons]
3. [Challenge 3 with resolution and lessons]

### Critical Insights
- **Technical**: [Key technical insight with recommendation]
- **Process**: [Key process insight with recommendation]
- **Business**: [Key business insight with recommendation]

### Priority Actions
1. [Immediate action with owner and timeline]
2. [Short-term improvement with owner and timeline]
3. [Medium-term initiative with owner and timeline]

### Memory Bank Updates
- [List of specific Memory Bank updates needed]
```

## 🚨 REFLECTION ENFORCEMENT PRINCIPLE

```
┌─────────────────────────────────────────────────────┐
│ COMPREHENSIVE REFLECTION IS MANDATORY for Level 4    │
│ tasks. Archiving CANNOT proceed until reflection     │
│ is completed and verified.                           │
└─────────────────────────────────────────────────────┘
``` 